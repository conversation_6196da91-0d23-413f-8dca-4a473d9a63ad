<?php

declare(strict_types=1);

/**
 * SPDX-FileCopyrightText: 2019 Nextcloud GmbH and Nextcloud contributors
 * SPDX-License-Identifier: AGPL-3.0-or-later
 */
namespace OC\Core\Command\Maintenance\Mimetype;

class GenerateMimetypeFileBuilder {
	/**
	 * Generate mime type list file
	 *
	 * @param array<string,string> $aliases
	 * @return string
	 */
	public function generateFile(array $aliases): string {
		// Remove comments
		$aliases = array_filter($aliases, static function ($key) {
			// Single digit extensions will be treated as integers
			// Let's make sure they are strings
			// https://github.com/nextcloud/server/issues/42902
			$key = (string)$key;
			return !($key === '' || $key[0] === '_');
		}, ARRAY_FILTER_USE_KEY);

		// Fetch all files
		$dir = new \DirectoryIterator(\OC::$SERVERROOT . '/core/img/filetypes');

		$files = [];
		foreach ($dir as $fileInfo) {
			if ($fileInfo->isFile()) {
				$file = preg_replace('/.[^.]*$/', '', $fileInfo->getFilename());
				$files[] = $file;
			}
		}

		//Remove duplicates
		$files = array_values(array_unique($files));
		sort($files);

		// Fetch all themes!
		$themes = [];
		$dirs = new \DirectoryIterator(\OC::$SERVERROOT . '/themes/');
		foreach ($dirs as $dir) {
			//Valid theme dir
			if ($dir->isFile() || $dir->isDot()) {
				continue;
			}

			$theme = $dir->getFilename();
			$themeDir = $dir->getPath() . '/' . $theme . '/core/img/filetypes/';
			// Check if this theme has its own filetype icons
			if (!file_exists($themeDir)) {
				continue;
			}

			$themes[$theme] = [];
			// Fetch all the theme icons!
			$themeIt = new \DirectoryIterator($themeDir);
			foreach ($themeIt as $fileInfo) {
				if ($fileInfo->isFile()) {
					$file = preg_replace('/.[^.]*$/', '', $fileInfo->getFilename());
					$themes[$theme][] = $file;
				}
			}

			//Remove Duplicates
			$themes[$theme] = array_values(array_unique($themes[$theme]));
			sort($themes[$theme]);
		}

		//Generate the JS
		return '/**
* This file is automatically generated
* DO NOT EDIT MANUALLY!
*
* You can update the list of MimeType Aliases in config/mimetypealiases.json
* The list of files is fetched from core/img/filetypes
* To regenerate this file run ./occ maintenance:mimetype:update-js
*/
OC.MimeTypeList={
	aliases: ' . json_encode($aliases, JSON_PRETTY_PRINT | JSON_UNESCAPED_SLASHES) . ',
	files: ' . json_encode($files, JSON_PRETTY_PRINT) . ',
	themes: ' . json_encode($themes, JSON_PRETTY_PRINT) . '
};
';
	}
}
