<?php

/**
 * SPDX-FileCopyrightText: 2016-2024 Nextcloud GmbH and Nextcloud contributors
 * SPDX-FileCopyrightText: 2011-2016 ownCloud, Inc.
 * SPDX-License-Identifier: AGPL-3.0-only
 *
 * @var \OCP\IL10N $l
 */
script('core', 'login');
?>
<div>
	<div id="login"></div>

	<!-- LaoId SSO Login Button -->
	<div style="text-align: center; margin-top: 20px;">
		<div style="margin-bottom: 15px; color: #666; font-size: 14px;">
			or
		</div>
		<button id="laoid-signin"
		        style="
		            background: #ff6600;
		            color: white;
		            border: none;
		            padding: 12px 24px;
		            border-radius: 5px;
		            font-size: 16px;
		            cursor: pointer;
		            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
		            transition: background-color 0.3s ease;
		        ">
			🔐 Login by LaoId
		</button>

		<div style="margin-top: 10px; font-size: 12px; color: #999;">
			Single Sign-On with LaoId
		</div>






	</div>
</div>

<script nonce="<?php p($_['cspNonce']); ?>">
document.addEventListener('DOMContentLoaded', function() {
	const laoidBtn = document.getElementById('laoid-signin');
	if (laoidBtn) {
		// Add hover effects
		laoidBtn.addEventListener('mouseover', function() {
			this.style.backgroundColor = '#e55a00';
		});

		laoidBtn.addEventListener('mouseout', function() {
			this.style.backgroundColor = '#ff6600';
		});

		// Add click handler
		laoidBtn.addEventListener('click', function(e) {
			const clientId = '660dfa27-5a95-4c88-8a55-abe1310bf579';
			const redirectUri = 'http://localhost';
			const loginUrl = `https://demo-sso.tinasoft.io/login?client_id=${clientId}&redirect_uri=${encodeURIComponent(redirectUri)}&use_callback_uri=true`;
			window.location.href = loginUrl;
		});
	}



	// Function to handle authorization code and get user info
	async function handleAuthorizationCode(authorizationCode) {
		try {
			// Step 1: Exchange authorization code for access token
			const tokenResponse = await fetch('https://demo-sso.tinasoft.io/api/v1/third-party/verify', {
				method: 'POST',
				headers: {
					'Content-Type': 'application/json',
					'Accept': 'application/json',
					'X-Accept-Language': 'vi'
				},
				body: JSON.stringify({
					code: authorizationCode,
					clientId: '660dfa27-5a95-4c88-8a55-abe1310bf579',
					clientSecret: 'df1699140bcb456eaa6d85d54c5fbd79'
				})
			});

			const tokenData = await tokenResponse.json();

			if (tokenData.success && tokenData.data && tokenData.data.accessToken) {
				// Step 3: Get user information using access token
				const userResponse = await fetch('https://demo-sso.tinasoft.io/api/v1/third-party/me', {
					method: 'GET',
					headers: {
						'Content-Type': 'application/json',
						'Accept': 'application/json',
						'X-Accept-Language': 'vi',
						'Authorization': 'Bearer ' + tokenData.data.accessToken,
						'x-api-key': '660dfa27-5a95-4c88-8a55-abe1310bf579'
					}
				});

				const userData = await userResponse.json();

				if (userData.success && userData.data) {
					displayUserInfo(userData.data);
				} else {
					alert('Failed to get user information: ' + (userData.message || 'Unknown error'));
				}
			} else {
				alert('Failed to get access token: ' + (tokenData.message || 'Unknown error'));
			}
		} catch (error) {
			alert('Error: ' + error.message);
		}
	}

	// Function to display user information
	function displayUserInfo(userInfo) {
		// Extract user data according to LaoID API format
		const fullName = `${userInfo.firstName || ''} ${userInfo.lastName || ''}`.trim();
		const primaryEmail = userInfo.email && userInfo.email.length > 0 ?
			userInfo.email.find(e => e.primary)?.email || userInfo.email[0].email : 'N/A';
		const primaryPhone = userInfo.phoneNumber && userInfo.phoneNumber.length > 0 ?
			userInfo.phoneNumber.find(p => p.primary)?.phoneNumber || userInfo.phoneNumber[0].phoneNumber : 'N/A';

		// Create a simple display div
		const userDisplay = document.createElement('div');
		userDisplay.style.cssText = `
			position: fixed;
			top: 20px;
			right: 20px;
			background: #fff;
			border: 2px solid #ff6600;
			border-radius: 10px;
			padding: 20px;
			box-shadow: 0 4px 12px rgba(0,0,0,0.15);
			z-index: 10000;
			max-width: 350px;
			font-family: Arial, sans-serif;
		`;

		userDisplay.innerHTML = `
			<h3 style="margin: 0 0 15px 0; color: #ff6600; font-size: 18px;">
				🎉 LaoID Login Success!
			</h3>
			${userInfo.avatar ? `
				<div style="text-align: center; margin-bottom: 15px;">
					<img src="${userInfo.avatar}" alt="Avatar" style="
						width: 60px;
						height: 60px;
						border-radius: 50%;
						border: 2px solid #ff6600;
					">
				</div>
			` : ''}
			<div style="margin-bottom: 10px;">
				<strong>👤 Name:</strong> ${fullName || userInfo.username || 'N/A'}
			</div>
			<div style="margin-bottom: 10px;">
				<strong>📧 Email:</strong> ${primaryEmail}
			</div>
			<div style="margin-bottom: 10px;">
				<strong>📱 Phone:</strong> ${primaryPhone}
			</div>
			<div style="margin-bottom: 10px;">
				<strong>🆔 User ID:</strong> ${userInfo.id || 'N/A'}
			</div>
			<div style="margin-bottom: 10px;">
				<strong>🌍 Country:</strong> ${userInfo.countryName || userInfo.country || 'N/A'}
			</div>
			<div style="margin-bottom: 15px;">
				<strong>🗣️ Language:</strong> ${userInfo.language || 'N/A'}
			</div>
			<button onclick="this.parentElement.remove()" style="
				background: #ff6600;
				color: white;
				border: none;
				padding: 8px 16px;
				border-radius: 5px;
				cursor: pointer;
				font-size: 14px;
				width: 100%;
			">Close</button>
		`;

		document.body.appendChild(userDisplay);

		// Auto remove after 60 seconds
		setTimeout(() => {
			if (userDisplay.parentElement) {
				userDisplay.remove();
			}
		}, 60000);
	}

	// Check for authorization code in URL
	function checkUrlForAuthCode() {
		const urlParams = new URLSearchParams(window.location.search);
		const authCode = urlParams.get('authorization_code');

		if (authCode) {
			handleAuthorizationCode(authCode);

			// Clean URL
			const cleanUrl = window.location.pathname;
			window.history.replaceState({}, document.title, cleanUrl);
		}
	}

	// Check URL on page load
	checkUrlForAuthCode();
});
</script>
