<?php

/**
 * SPDX-FileCopyrightText: 2016-2024 Nextcloud GmbH and Nextcloud contributors
 * SPDX-FileCopyrightText: 2011-2016 ownCloud, Inc.
 * SPDX-License-Identifier: AGPL-3.0-only
 *
 * @var \OCP\IL10N $l
 */
script('core', 'login');
?>
<div>
	<div id="login"></div>

	<!-- LaoId SSO Login Button -->
	<div style="text-align: center; margin-top: 20px;">
		<div style="margin-bottom: 15px; color: #666; font-size: 14px;">
			or
		</div>
		<button id="laoid-signin"
		        style="
		            background: #ff6600;
		            color: white;
		            border: none;
		            padding: 12px 24px;
		            border-radius: 5px;
		            font-size: 16px;
		            cursor: pointer;
		            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
		            transition: background-color 0.3s ease;
		        ">
			🔐 Login by LaoId
		</button>

		<!-- Manual Authorization Code Input -->
		<div style="margin-top: 15px;">
			<input type="text" id="manual-auth-code" placeholder="Enter authorization code from email" style="
				width: 100%;
				padding: 12px;
				border: 2px solid #ddd;
				border-radius: 5px;
				font-size: 14px;
				margin-bottom: 10px;
				box-sizing: border-box;
			">
			<button id="manual-submit" style="
				background: #28a745;
				color: white;
				border: none;
				padding: 12px 20px;
				border-radius: 5px;
				cursor: pointer;
				font-size: 14px;
				font-weight: bold;
				width: 100%;
				box-shadow: 0 2px 4px rgba(0,0,0,0.1);
			">Submit Authorization Code</button>
		</div>

		<div style="margin-top: 10px; font-size: 12px; color: #999;">
			Single Sign-On with LaoId
		</div>






	</div>
</div>

<script nonce="<?php p($_['cspNonce']); ?>">
document.addEventListener('DOMContentLoaded', function() {
	console.log('DOM loaded, looking for LaoID button...');

	const laoidBtn = document.getElementById('laoid-signin');
	if (laoidBtn) {
		console.log('✅ LaoID button found!');

		// Add hover effects
		laoidBtn.addEventListener('mouseover', function() {
			this.style.backgroundColor = '#e55a00';
		});

		laoidBtn.addEventListener('mouseout', function() {
			this.style.backgroundColor = '#ff6600';
		});

		// Add click handler for debugging
		laoidBtn.addEventListener('click', function(e) {
			console.log('🔥 LaoID button clicked!');

			// Check if SDK is available
			if (typeof window.LaoIdSSO !== 'undefined') {
				console.log('✅ LaoID SDK is available');

				// Debug: Check current meta tag values
				const clientIdMeta = document.querySelector('meta[name="laoid-signin-client-id"]');
				const redirectUriMeta = document.querySelector('meta[name="laoid-signin-redirect-uri"]');
				const useCallbackMeta = document.querySelector('meta[name="laoid-signin-use-callback-uri"]');

				const currentClientId = clientIdMeta ? clientIdMeta.getAttribute('content') : 'NOT FOUND';
				const currentRedirectUri = redirectUriMeta ? redirectUriMeta.getAttribute('content') : 'NOT FOUND';
				const currentUseCallback = useCallbackMeta ? useCallbackMeta.getAttribute('content') : 'NOT FOUND';

				console.log('🔍 Current Meta Values:');
				console.log('  Client ID:', currentClientId);
				console.log('  Redirect URI:', currentRedirectUri);
				console.log('  Use Callback:', currentUseCallback);

				// Check if client ID is still placeholder
				if (currentClientId === 'YOUR_CLIENT_ID_HERE') {
					console.warn('⚠️ Client ID is still placeholder! Need to update with real ClientID from LaoCRM');
					alert('⚠️ Cần cập nhật ClientID từ LaoCRM!\n\nClient ID hiện tại: ' + currentClientId);
					return;
				}

				// Try manual redirect since SDK auto-handling might not work
				console.log('🔄 Trying manual redirect...');
				const loginUrl = `https://demo-sso.tinasoft.io/login?client_id=${encodeURIComponent(currentClientId)}&redirect_uri=${encodeURIComponent(currentRedirectUri)}&use_callback_uri=${currentUseCallback}`;
				console.log('🔗 Opening:', loginUrl);
				window.location.href = loginUrl;

			} else {
				console.warn('❌ LaoID SDK not loaded, will try manual redirect');
				// Fallback: manual redirect
				const clientId = '660dfa27-5a95-4c88-8a55-abe1310bf579';
				const redirectUri = 'http://localhost';
				const loginUrl = `https://demo-sso.tinasoft.io/login?client_id=${clientId}&redirect_uri=${encodeURIComponent(redirectUri)}&use_callback_uri=true`;
				window.location.href = loginUrl;
			}
		});

		console.log('✅ Event listeners added to LaoID button');
	} else {
		console.error('❌ LaoID button not found!');
	}

	// Test button for comparison
	const testBtn = document.getElementById('test-btn');
	if (testBtn) {
		testBtn.addEventListener('click', function() {
			alert('Test button works!');
			console.log('✅ Test button clicked successfully');
		});
		console.log('✅ Test button event listener added');
	}



	// Check if SDK is loaded and debug config
	setTimeout(function() {
		if (typeof window.LaoIdSSO !== 'undefined') {
			console.log('✅ LaoID Demo SDK loaded successfully');
			console.log('🔍 SDK Object:', window.LaoIdSSO);

			// Debug: Check what config SDK is reading
			const clientIdMeta = document.querySelector('meta[name="laoid-signin-client-id"]');
			const redirectUriMeta = document.querySelector('meta[name="laoid-signin-redirect-uri"]');
			const useCallbackMeta = document.querySelector('meta[name="laoid-signin-use-callback-uri"]');

			console.log('🔍 Meta Tags Config:');
			console.log('  Client ID:', clientIdMeta ? clientIdMeta.getAttribute('content') : 'NOT FOUND');
			console.log('  Redirect URI:', redirectUriMeta ? redirectUriMeta.getAttribute('content') : 'NOT FOUND');
			console.log('  Use Callback:', useCallbackMeta ? useCallbackMeta.getAttribute('content') : 'NOT FOUND');

			console.log('🔍 SDK Config:');
			console.log('  SDK Client ID:', window.LaoIdSSO.clientId);
			console.log('  SDK Redirect URI:', window.LaoIdSSO.redirectUri);
			console.log('  SDK Use Callback:', window.LaoIdSSO.useCallbackUri);
			console.log('  SDK API Endpoint:', window.LaoIdSSO.apiEndpoint);
		} else {
			console.warn('⚠️ LaoID SDK not loaded after 2 seconds');
		}
	}, 2000);

	// Listen for LaoID callback messages (Cách 2: use-callback-uri=false)
	const onMessage = (event) => {
		if (event.origin !== "https://demo-sso.tinasoft.io") return;

		console.log('📨 Received message from LaoID:', event.data);
		console.log('📨 Full event object:', event);

		const {message, data, statusCode, errorMessage} = event.data || {};

		if (message === 'login_success' && data) {
			console.log('✅ LaoID Login Success!');
			console.log('🔑 Authorization Code:', data);
			handleAuthorizationCode(data);
		} else if (message === 'login_fail') {
			console.error('❌ LaoID Login Failed:', data);
			alert('LaoID Login Failed: ' + (data || 'Unknown error'));
		} else if (statusCode) {
			// Handle error responses with status codes
			console.error('❌ LaoID Error Response:', event.data);
			let errorMsg = `Error ${statusCode}: ${errorMessage || 'Unknown error'}`;

			// Add specific error explanations
			switch(statusCode) {
				case 'LOGIN0243':
					errorMsg += '\n(3G login verification failed - Please try again)';
					break;
				case 'OTP0030':
					errorMsg += '\n(Invalid password or OTP - Please check your credentials)';
					break;
				case 'CAPTCHA_REQUIRED':
					errorMsg += '\n(Captcha verification required)';
					break;
			}

			alert(errorMsg);
		} else {
			console.log('📨 Other message:', {message, data, statusCode, errorMessage});
		}
	};

	window.addEventListener("message", onMessage, false);

	// Function to handle authorization code and get user info
	async function handleAuthorizationCode(authorizationCode) {
		console.log('🔄 Processing authorization code:', authorizationCode);
		console.log('🔑 Using client credentials:');
		console.log('  Client ID: 660dfa27-5a95-4c88-8a55-abe1310bf579');
		console.log('  Client Secret: df1699140bcb456eaa6d85d54c5fbd79');

		try {
			// Step 1: Exchange authorization code for access token
			// URL: https://sso.laoid.net/api/v1/third-party/verify (Production)
			// URL: https://demo-sso.tinasoft.io/api/v1/third-party/verify (Demo)
			const tokenResponse = await fetch('https://demo-sso.tinasoft.io/api/v1/third-party/verify', {
				method: 'POST',
				headers: {
					'Content-Type': 'application/json',
					'Accept': 'application/json',
					'X-Accept-Language': 'vi' // Vietnamese language
				},
				body: JSON.stringify({
					code: authorizationCode, // Changed from authorizationCode to code
					clientId: '660dfa27-5a95-4c88-8a55-abe1310bf579',
					clientSecret: 'df1699140bcb456eaa6d85d54c5fbd79'
				})
			});

			const tokenData = await tokenResponse.json();
			console.log('🔑 Token Response:', tokenData);
			console.log('📊 Status Code:', tokenData.statusCode);
			console.log('📊 HTTP Status:', tokenResponse.status);
			console.log('📊 Response Headers:', Object.fromEntries(tokenResponse.headers.entries()));

			if (tokenData.success && tokenData.data && tokenData.data.accessToken) {
				console.log('✅ Access token received successfully');
				console.log('⏰ Token expires in:', tokenData.data.expiresIn, 'seconds');

				// Step 2 (Optional): Verify access token
				await verifyAccessToken(tokenData.data.accessToken);

				// Step 3: Get user information using access token
				// URL: https://sso.laoid.net/api/v1/third-party/me (Production)
				// URL: https://demo-sso.tinasoft.io/api/v1/third-party/me (Demo)
				const userResponse = await fetch('https://demo-sso.tinasoft.io/api/v1/third-party/me', {
					method: 'GET',
					headers: {
						'Content-Type': 'application/json',
						'Accept': 'application/json',
						'X-Accept-Language': 'vi',
						'Authorization': 'Bearer ' + tokenData.data.accessToken,
						'x-api-key': '660dfa27-5a95-4c88-8a55-abe1310bf579' // Client ID as API key
					}
				});

				const userData = await userResponse.json();
				console.log('👤 User Data Response:', userData);
				console.log('📊 User Status Code:', userData.statusCode);

				if (userData.success && userData.data) {
					console.log('✅ User information retrieved successfully');
					displayUserInfo(userData.data, tokenData.data);
				} else {
					console.error('❌ Failed to get user info:', userData);
					alert('Failed to get user information: ' + (userData.message || 'Unknown error') + '\nStatus: ' + (userData.statusCode || 'N/A'));
				}
			} else {
				console.error('❌ Failed to get access token:', tokenData);
				let errorMsg = 'Failed to get access token: ' + (tokenData.message || 'Unknown error');
				if (tokenData.statusCode) {
					errorMsg += '\nStatus Code: ' + tokenData.statusCode;
					// Add specific error messages based on status codes
					switch(tokenData.statusCode) {
						case 'THIRDPARTY0208':
							errorMsg += '\n(Invalid client credentials)';
							break;
						case 'THIRDPARTY0209':
							errorMsg += '\n(Invalid authorization code)';
							break;
						case 'THIRDPARTY0217':
							errorMsg += '\n(Authorization code does not belong to this client)';
							break;
					}
				}
				alert(errorMsg);
			}
		} catch (error) {
			console.error('❌ Error processing authorization code:', error);

			// Show detailed error information
			const errorDisplay = document.createElement('div');
			errorDisplay.style.cssText = `
				position: fixed;
				top: 50%;
				left: 50%;
				transform: translate(-50%, -50%);
				background: #fff;
				border: 2px solid #dc3545;
				border-radius: 10px;
				padding: 30px;
				box-shadow: 0 4px 12px rgba(0,0,0,0.15);
				z-index: 10000;
				max-width: 500px;
				font-family: Arial, sans-serif;
			`;

			errorDisplay.innerHTML = `
				<h3 style="margin: 0 0 15px 0; color: #dc3545; font-size: 18px;">
					❌ Authentication Error
				</h3>
				<div style="margin-bottom: 15px; font-size: 14px;">
					<strong>Error:</strong> ${error.message}
				</div>
				<div style="text-align: center;">
					<button onclick="this.parentElement.remove(); window.location.reload();" style="
						background: #dc3545;
						color: white;
						border: none;
						padding: 10px 20px;
						border-radius: 5px;
						cursor: pointer;
						font-size: 14px;
					">Try Again</button>
				</div>
			`;

			document.body.appendChild(errorDisplay);
		}
	}

	// Step 2 (Optional): Verify access token
	async function verifyAccessToken(accessToken) {
		console.log('🔍 Verifying access token...');

		try {
			const verifyResponse = await fetch('https://demo-sso.tinasoft.io/api/v1/third-party/token-verifier', {
				method: 'POST',
				headers: {
					'Content-Type': 'application/json',
					'Accept': 'application/json',
					'X-Accept-Language': 'vi'
				},
				body: JSON.stringify({
					clientId: '660dfa27-5a95-4c88-8a55-abe1310bf579',
					clientSecret: 'df1699140bcb456eaa6d85d54c5fbd79',
					AccessToken: accessToken // Note: Capital 'A' as per documentation
				})
			});

			const verifyData = await verifyResponse.json();
			console.log('🔍 Token Verification Response:', verifyData);
			console.log('📊 Verification Status Code:', verifyData.statusCode);

			if (verifyData.success && verifyData.data && verifyData.data.isVerified) {
				console.log('✅ Access token is valid');
				return true;
			} else {
				console.warn('⚠️ Access token verification failed:', verifyData);
				// Continue anyway since this step is optional
				return false;
			}
		} catch (error) {
			console.warn('⚠️ Token verification error (continuing anyway):', error);
			return false;
		}
	}

	// Function to display user information
	function displayUserInfo(userInfo, tokenInfo) {
		console.log('🎉 Displaying user info:', userInfo);
		console.log('🔑 Token info:', tokenInfo);

		// Extract user data according to LaoID API format
		const fullName = `${userInfo.firstName || ''} ${userInfo.lastName || ''}`.trim();
		const primaryEmail = userInfo.email && userInfo.email.length > 0
			? userInfo.email.find(e => e.primary)?.email || userInfo.email[0]?.email
			: 'N/A';
		const primaryPhone = userInfo.phoneNumber && userInfo.phoneNumber.length > 0
			? userInfo.phoneNumber.find(p => p.primary)?.phoneNumber || userInfo.phoneNumber[0]?.phoneNumber
			: 'N/A';

		// Hide login form and show user info page
		const loginForm = document.querySelector('.login-form');
		if (loginForm) {
			loginForm.style.display = 'none';
		}

		// Create full page user info display
		const userInfoPage = document.createElement('div');
		userInfoPage.style.cssText = `
			position: fixed;
			top: 0;
			left: 0;
			width: 100%;
			height: 100%;
			background: linear-gradient(135deg, #ff6600, #ff8533);
			z-index: 10000;
			display: flex;
			align-items: center;
			justify-content: center;
			font-family: Arial, sans-serif;
		`;

		userInfoPage.innerHTML = `
			<div style="
				background: white;
				border-radius: 20px;
				padding: 40px;
				box-shadow: 0 10px 30px rgba(0,0,0,0.3);
				max-width: 500px;
				width: 90%;
				text-align: center;
			">
				<h1 style="margin: 0 0 30px 0; color: #ff6600; font-size: 28px;">
					🎉 Welcome to Nextcloud!
				</h1>

				${userInfo.avatar ? `
					<div style="margin-bottom: 20px;">
						<img src="${userInfo.avatar}" alt="Avatar" style="
							width: 100px;
							height: 100px;
							border-radius: 50%;
							border: 4px solid #ff6600;
						">
					</div>
				` : ''}

				<h2 style="margin: 0 0 20px 0; color: #333; font-size: 24px;">
					${fullName || userInfo.username || 'User'}
				</h2>

				<div style="text-align: left; margin: 20px 0;">
					<div style="margin-bottom: 12px; font-size: 16px; padding: 10px; background: #f8f9fa; border-radius: 8px;">
						<strong>📧 Email:</strong> ${primaryEmail}
					</div>
					<div style="margin-bottom: 12px; font-size: 16px; padding: 10px; background: #f8f9fa; border-radius: 8px;">
						<strong>📱 Phone:</strong> ${primaryPhone}
					</div>
					<div style="margin-bottom: 12px; font-size: 16px; padding: 10px; background: #f8f9fa; border-radius: 8px;">
						<strong>🆔 User ID:</strong> ${userInfo.id || 'N/A'}
					</div>
					<div style="margin-bottom: 12px; font-size: 16px; padding: 10px; background: #f8f9fa; border-radius: 8px;">
						<strong>👤 Username:</strong> ${userInfo.username || 'N/A'}
					</div>
					<div style="margin-bottom: 12px; font-size: 16px; padding: 10px; background: #f8f9fa; border-radius: 8px;">
						<strong>🎂 Birthday:</strong> ${userInfo.dateOfBirth || 'N/A'}
					</div>
					<div style="margin-bottom: 12px; font-size: 16px; padding: 10px; background: #f8f9fa; border-radius: 8px;">
						<strong>🌍 Country:</strong> ${userInfo.countryName || 'N/A'}
					</div>
				</div>

				<div style="margin: 30px 0 20px 0; text-align: center;">
					<button onclick="window.location.reload()" style="
						background: #ff6600;
						color: white;
						border: none;
						padding: 15px 30px;
						border-radius: 10px;
						cursor: pointer;
						font-size: 16px;
						margin-right: 15px;
						font-weight: bold;
					">Login Again</button>
					<button onclick="window.location.href='/'" style="
						background: #28a745;
						color: white;
						border: none;
						padding: 15px 30px;
						border-radius: 10px;
						cursor: pointer;
						font-size: 16px;
						font-weight: bold;
					">Continue to Nextcloud</button>
				</div>

				<div style="font-size: 12px; color: #666; margin-top: 20px;">
					✅ Successfully authenticated with LaoID<br>
					⏰ Session expires in ${Math.floor((tokenInfo?.expiresIn || 0) / 3600)} hours
				</div>
			</div>
		`;

		document.body.appendChild(userInfoPage);
	}

	// Check for authorization code in URL (Cách 1: use-callback-uri=true)
	function checkUrlForAuthCode() {
		const urlParams = new URLSearchParams(window.location.search);
		const authCode = urlParams.get('authorization_code');
		const error = urlParams.get('error');

		if (authCode) {
			console.log('🔗 Found authorization code in URL:', authCode);
			handleAuthorizationCode(authCode);

			// Clean URL
			const cleanUrl = window.location.pathname;
			window.history.replaceState({}, document.title, cleanUrl);
		} else if (error) {
			console.error('❌ Found error in URL:', error);
			alert('LaoID Authentication Error: ' + error);

			// Clean URL
			const cleanUrl = window.location.pathname;
			window.history.replaceState({}, document.title, cleanUrl);
		}
	}

	// Check URL on page load
	checkUrlForAuthCode();
});

// Test function for different callback URIs
function testCallback(callbackUri) {
	console.log('🧪 Testing callback URI:', callbackUri);

	// Update meta tag
	const metaRedirectUri = document.querySelector('meta[name="laoid-signin-redirect-uri"]');
	if (metaRedirectUri) {
		metaRedirectUri.setAttribute('content', callbackUri);
		console.log('✅ Updated meta tag to:', callbackUri);
	}

	// Manual redirect for testing
	const clientId = '660dfa27-5a95-4c88-8a55-abe1310bf579';
	const loginUrl = `https://sso.laoid.net/login?client_id=${clientId}&redirect_uri=${encodeURIComponent(callbackUri)}&use_callback_uri=true`;

	console.log('🔗 Opening:', loginUrl);
	window.open(loginUrl, '_blank');
}

// Test function for different client IDs
function testClientId(clientId) {
	console.log('🔑 Testing client ID:', clientId);

	// Update meta tag
	const metaClientId = document.querySelector('meta[name="laoid-signin-client-id"]');
	if (metaClientId) {
		metaClientId.setAttribute('content', clientId);
		console.log('✅ Updated client ID meta tag to:', clientId);
	}

	// Manual redirect for testing
	const redirectUri = 'https://localhost';
	const loginUrl = `https://sso.laoid.net/login?client_id=${clientId}&redirect_uri=${encodeURIComponent(redirectUri)}&use_callback_uri=true`;

	console.log('🔗 Opening:', loginUrl);
	window.open(loginUrl, '_blank');
}

// Test with real ClientID
function testWithRealClientID() {
	console.log('🧪 Testing with real ClientID...');

	const clientId = '660dfa27-5a95-4c88-8a55-abe1310bf579';
	const redirectUri = 'http://localhost:8888';
	const loginUrl = `https://sso.laoid.net/login?client_id=${clientId}&redirect_uri=${encodeURIComponent(redirectUri)}&use_callback_uri=true`;

	console.log('🔗 Opening with real ClientID:', loginUrl);
	window.location.href = loginUrl;
}


</script>
