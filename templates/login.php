<?php

/**
 * SPDX-FileCopyrightText: 2016-2024 Nextcloud GmbH and Nextcloud contributors
 * SPDX-FileCopyrightText: 2011-2016 ownCloud, Inc.
 * SPDX-License-Identifier: AGPL-3.0-only
 *
 * @var \OCP\IL10N $l
 */
script('core', 'login');
?>
<div>
	<div id="login"></div>

	<!-- LaoId SSO Login Button -->
	<div style="text-align: center; margin-top: 20px;">
		<div style="margin-bottom: 15px; color: #666; font-size: 14px;">
			or
		</div>
		<button id="laoid-signin"
		        style="
		            background: #ff6600;
		            color: white;
		            border: none;
		            padding: 12px 24px;
		            border-radius: 5px;
		            font-size: 16px;
		            cursor: pointer;
		            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
		            transition: background-color 0.3s ease;
		        ">
			🔐 Login by LaoId
		</button>

		<div style="margin-top: 10px; font-size: 12px; color: #999;">
			Single Sign-On with LaoId
		</div>






	</div>
</div>

<script nonce="<?php p($_['cspNonce']); ?>">
document.addEventListener('DOMContentLoaded', function() {
	console.log('DOM loaded, looking for LaoID button...');

	const laoidBtn = document.getElementById('laoid-signin');
	if (laoidBtn) {
		console.log('✅ LaoID button found!');

		// Add hover effects
		laoidBtn.addEventListener('mouseover', function() {
			this.style.backgroundColor = '#e55a00';
		});

		laoidBtn.addEventListener('mouseout', function() {
			this.style.backgroundColor = '#ff6600';
		});

		// Add click handler for debugging
		laoidBtn.addEventListener('click', function(e) {
			console.log('🔥 LaoID button clicked!');

			// Check if SDK is available
			if (typeof window.LaoIdSSO !== 'undefined') {
				console.log('✅ LaoID SDK is available');

				// Debug: Check current meta tag values
				const clientIdMeta = document.querySelector('meta[name="laoid-signin-client-id"]');
				const redirectUriMeta = document.querySelector('meta[name="laoid-signin-redirect-uri"]');
				const useCallbackMeta = document.querySelector('meta[name="laoid-signin-use-callback-uri"]');

				const currentClientId = clientIdMeta ? clientIdMeta.getAttribute('content') : 'NOT FOUND';
				const currentRedirectUri = redirectUriMeta ? redirectUriMeta.getAttribute('content') : 'NOT FOUND';
				const currentUseCallback = useCallbackMeta ? useCallbackMeta.getAttribute('content') : 'NOT FOUND';

				console.log('🔍 Current Meta Values:');
				console.log('  Client ID:', currentClientId);
				console.log('  Redirect URI:', currentRedirectUri);
				console.log('  Use Callback:', currentUseCallback);

				// Check if client ID is still placeholder
				if (currentClientId === 'YOUR_CLIENT_ID_HERE') {
					console.warn('⚠️ Client ID is still placeholder! Need to update with real ClientID from LaoCRM');
					alert('⚠️ Cần cập nhật ClientID từ LaoCRM!\n\nClient ID hiện tại: ' + currentClientId);
					return;
				}

				// Try manual redirect since SDK auto-handling might not work
				console.log('🔄 Trying manual redirect...');
				const loginUrl = `https://demo-sso.tinasoft.io/login?client_id=${encodeURIComponent(currentClientId)}&redirect_uri=${encodeURIComponent(currentRedirectUri)}&use_callback_uri=${currentUseCallback}`;
				console.log('🔗 Opening:', loginUrl);
				window.location.href = loginUrl;

			} else {
				console.warn('❌ LaoID SDK not loaded, will try manual redirect');
				// Fallback: manual redirect
				const clientId = '660dfa27-5a95-4c88-8a55-abe1310bf579';
				const redirectUri = 'http://localhost';
				const loginUrl = `https://demo-sso.tinasoft.io/login?client_id=${clientId}&redirect_uri=${encodeURIComponent(redirectUri)}&use_callback_uri=true`;
				window.location.href = loginUrl;
			}
		});

		console.log('✅ Event listeners added to LaoID button');
	} else {
		console.error('❌ LaoID button not found!');
	}

	// Test button for comparison
	const testBtn = document.getElementById('test-btn');
	if (testBtn) {
		testBtn.addEventListener('click', function() {
			alert('Test button works!');
			console.log('✅ Test button clicked successfully');
		});
		console.log('✅ Test button event listener added');
	}



	// Check if SDK is loaded and debug config
	setTimeout(function() {
		if (typeof window.LaoIdSSO !== 'undefined') {
			console.log('✅ LaoID Demo SDK loaded successfully');
			console.log('🔍 SDK Object:', window.LaoIdSSO);

			// Debug: Check what config SDK is reading
			const clientIdMeta = document.querySelector('meta[name="laoid-signin-client-id"]');
			const redirectUriMeta = document.querySelector('meta[name="laoid-signin-redirect-uri"]');
			const useCallbackMeta = document.querySelector('meta[name="laoid-signin-use-callback-uri"]');

			console.log('🔍 Meta Tags Config:');
			console.log('  Client ID:', clientIdMeta ? clientIdMeta.getAttribute('content') : 'NOT FOUND');
			console.log('  Redirect URI:', redirectUriMeta ? redirectUriMeta.getAttribute('content') : 'NOT FOUND');
			console.log('  Use Callback:', useCallbackMeta ? useCallbackMeta.getAttribute('content') : 'NOT FOUND');

			console.log('🔍 SDK Config:');
			console.log('  SDK Client ID:', window.LaoIdSSO.clientId);
			console.log('  SDK Redirect URI:', window.LaoIdSSO.redirectUri);
			console.log('  SDK Use Callback:', window.LaoIdSSO.useCallbackUri);
			console.log('  SDK API Endpoint:', window.LaoIdSSO.apiEndpoint);
		} else {
			console.warn('⚠️ LaoID SDK not loaded after 2 seconds');
		}
	}, 2000);

	// Listen for LaoID callback messages (Cách 2: use-callback-uri=false)
	const onMessage = (event) => {
		if (event.origin !== "https://demo-sso.tinasoft.io") return;

		console.log('📨 Received message from LaoID:', event.data);

		const {message, data} = event.data || {};

		if (message === 'login_success' && data) {
			console.log('✅ LaoID Login Success!');
			console.log('🔑 Authorization Code:', data);
			handleAuthorizationCode(data);
		} else if (message === 'login_fail') {
			console.error('❌ LaoID Login Failed:', data);
			alert('LaoID Login Failed: ' + (data || 'Unknown error'));
		} else {
			console.log('📨 Other message:', {message, data});
		}
	};

	window.addEventListener("message", onMessage, false);

	// Function to handle authorization code and get user info
	async function handleAuthorizationCode(authorizationCode) {
		console.log('🔄 Processing authorization code:', authorizationCode);
		console.log('🔑 Using client credentials:');
		console.log('  Client ID: 660dfa27-5a95-4c88-8a55-abe1310bf579');
		console.log('  Client Secret: df1699140bcb456eaa6d85d54c5fbd79');

		try {
			// Step 1: Exchange authorization code for access token
			// URL: https://demo-sso.tinasoft.io/api/v1/third-party/verify (Demo)
			console.log('📡 Step 1: Getting access token...');
			const tokenResponse = await fetch('https://demo-sso.tinasoft.io/api/v1/third-party/verify', {
				method: 'POST',
				headers: {
					'Content-Type': 'application/json',
					'Accept': 'application/json',
					'X-Accept-Language': 'vi'
				},
				body: JSON.stringify({
					code: authorizationCode,
					clientId: '660dfa27-5a95-4c88-8a55-abe1310bf579',
					clientSecret: 'df1699140bcb456eaa6d85d54c5fbd79'
				})
			});

			const tokenData = await tokenResponse.json();
			console.log('🔑 Token Response:', tokenData);
			console.log('📊 Status Code:', tokenData.statusCode);
			console.log('📊 HTTP Status:', tokenResponse.status);

			if (tokenData.success && tokenData.data && tokenData.data.accessToken) {
				console.log('✅ Access token received successfully');
				console.log('⏰ Token expires in:', tokenData.data.expiresIn, 'seconds');

				// Step 3: Get user information using access token
				// URL: https://demo-sso.tinasoft.io/api/v1/third-party/me (Demo)
				console.log('📡 Step 3: Getting user information...');
				const userResponse = await fetch('https://demo-sso.tinasoft.io/api/v1/third-party/me', {
					method: 'GET',
					headers: {
						'Content-Type': 'application/json',
						'Accept': 'application/json',
						'X-Accept-Language': 'vi',
						'Authorization': 'Bearer ' + tokenData.data.accessToken,
						'x-api-key': '660dfa27-5a95-4c88-8a55-abe1310bf579'
					}
				});

				const userData = await userResponse.json();
				console.log('👤 User Data Response:', userData);

				if (userData.success && userData.data) {
					console.log('✅ User information received successfully');
					displayUserInfo(userData.data);
				} else {
					console.error('❌ Failed to get user info:', userData);
					alert('Failed to get user information: ' + (userData.message || 'Unknown error'));
				}
			} else {
				console.error('❌ Failed to get access token:', tokenData);
				alert('Failed to get access token: ' + (tokenData.message || 'Unknown error'));
			}
		} catch (error) {
			console.error('❌ Error processing authorization code:', error);
			alert('Error: ' + error.message);
		}
	}

	// Function to display user information
	function displayUserInfo(userInfo) {
		console.log('🎉 Displaying user info:', userInfo);

		// Extract user data according to LaoID API format
		const fullName = `${userInfo.firstName || ''} ${userInfo.lastName || ''}`.trim();
		const primaryEmail = userInfo.email && userInfo.email.length > 0 ?
			userInfo.email.find(e => e.primary)?.email || userInfo.email[0].email : 'N/A';
		const primaryPhone = userInfo.phoneNumber && userInfo.phoneNumber.length > 0 ?
			userInfo.phoneNumber.find(p => p.primary)?.phoneNumber || userInfo.phoneNumber[0].phoneNumber : 'N/A';

		// Create a simple display div
		const userDisplay = document.createElement('div');
		userDisplay.style.cssText = `
			position: fixed;
			top: 20px;
			right: 20px;
			background: #fff;
			border: 2px solid #ff6600;
			border-radius: 10px;
			padding: 20px;
			box-shadow: 0 4px 12px rgba(0,0,0,0.15);
			z-index: 10000;
			max-width: 350px;
			font-family: Arial, sans-serif;
		`;

		userDisplay.innerHTML = `
			<h3 style="margin: 0 0 15px 0; color: #ff6600; font-size: 18px;">
				🎉 LaoID Login Success!
			</h3>
			${userInfo.avatar ? `
				<div style="text-align: center; margin-bottom: 15px;">
					<img src="${userInfo.avatar}" alt="Avatar" style="
						width: 60px;
						height: 60px;
						border-radius: 50%;
						border: 2px solid #ff6600;
					">
				</div>
			` : ''}
			<div style="margin-bottom: 10px;">
				<strong>👤 Name:</strong> ${fullName || userInfo.username || 'N/A'}
			</div>
			<div style="margin-bottom: 10px;">
				<strong>📧 Email:</strong> ${primaryEmail}
			</div>
			<div style="margin-bottom: 10px;">
				<strong>📱 Phone:</strong> ${primaryPhone}
			</div>
			<div style="margin-bottom: 10px;">
				<strong>🆔 User ID:</strong> ${userInfo.id || 'N/A'}
			</div>
			<div style="margin-bottom: 10px;">
				<strong>🌍 Country:</strong> ${userInfo.countryName || userInfo.country || 'N/A'}
			</div>
			<div style="margin-bottom: 15px;">
				<strong>🗣️ Language:</strong> ${userInfo.language || 'N/A'}
			</div>
			<button onclick="this.parentElement.remove()" style="
				background: #ff6600;
				color: white;
				border: none;
				padding: 8px 16px;
				border-radius: 5px;
				cursor: pointer;
				font-size: 14px;
				width: 100%;
			">Close</button>
		`;

		document.body.appendChild(userDisplay);

		// Auto remove after 60 seconds
		setTimeout(() => {
			if (userDisplay.parentElement) {
				userDisplay.remove();
			}
		}, 60000);
	}

	// Check for authorization code in URL (Cách 1: use-callback-uri=true)
	function checkUrlForAuthCode() {
		const urlParams = new URLSearchParams(window.location.search);
		const authCode = urlParams.get('authorization_code');

		if (authCode) {
			console.log('🔗 Found authorization code in URL:', authCode);
			handleAuthorizationCode(authCode);

			// Clean URL
			const cleanUrl = window.location.pathname;
			window.history.replaceState({}, document.title, cleanUrl);
		}
	}

	// Check URL on page load
	checkUrlForAuthCode();
});

// Test function for different callback URIs
function testCallback(callbackUri) {
	console.log('🧪 Testing callback URI:', callbackUri);

	// Update meta tag
	const metaRedirectUri = document.querySelector('meta[name="laoid-signin-redirect-uri"]');
	if (metaRedirectUri) {
		metaRedirectUri.setAttribute('content', callbackUri);
		console.log('✅ Updated meta tag to:', callbackUri);
	}

	// Manual redirect for testing
	const clientId = '660dfa27-5a95-4c88-8a55-abe1310bf579';
	const loginUrl = `https://sso.laoid.net/login?client_id=${clientId}&redirect_uri=${encodeURIComponent(callbackUri)}&use_callback_uri=true`;

	console.log('🔗 Opening:', loginUrl);
	window.open(loginUrl, '_blank');
}

// Test function for different client IDs
function testClientId(clientId) {
	console.log('🔑 Testing client ID:', clientId);

	// Update meta tag
	const metaClientId = document.querySelector('meta[name="laoid-signin-client-id"]');
	if (metaClientId) {
		metaClientId.setAttribute('content', clientId);
		console.log('✅ Updated client ID meta tag to:', clientId);
	}

	// Manual redirect for testing
	const redirectUri = 'https://localhost';
	const loginUrl = `https://sso.laoid.net/login?client_id=${clientId}&redirect_uri=${encodeURIComponent(redirectUri)}&use_callback_uri=true`;

	console.log('🔗 Opening:', loginUrl);
	window.open(loginUrl, '_blank');
}

// Test with real ClientID
function testWithRealClientID() {
	console.log('🧪 Testing with real ClientID...');

	const clientId = '660dfa27-5a95-4c88-8a55-abe1310bf579';
	const redirectUri = 'http://localhost:8888';
	const loginUrl = `https://sso.laoid.net/login?client_id=${clientId}&redirect_uri=${encodeURIComponent(redirectUri)}&use_callback_uri=true`;

	console.log('🔗 Opening with real ClientID:', loginUrl);
	window.location.href = loginUrl;
}


</script>
