<?php

/**
 * SPDX-FileCopyrightText: 2016-2024 Nextcloud GmbH and Nextcloud contributors
 * SPDX-FileCopyrightText: 2011-2016 ownCloud, Inc.
 * SPDX-License-Identifier: AGPL-3.0-only
 *
 * @var \OCP\IL10N $l
 */
script('core', 'login');
?>
<div>
	<div id="login"></div>

	<!-- LaoId SSO Login Button -->
	<div style="text-align: center; margin-top: 20px;">
		<div style="margin-bottom: 15px; color: #666; font-size: 14px;">
			or
		</div>
		<button id="laoid-signin"
		        style="
		            background: #ff6600;
		            color: white;
		            border: none;
		            padding: 12px 24px;
		            border-radius: 5px;
		            font-size: 16px;
		            cursor: pointer;
		            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
		            transition: background-color 0.3s ease;
		        ">
			🔐 Login by LaoId
		</button>

		<div style="margin-top: 10px; font-size: 12px; color: #999;">
			Single Sign-On with LaoId
		</div>






	</div>
</div>

<script nonce="<?php p($_['cspNonce']); ?>">
document.addEventListener('DOMContentLoaded', function() {
	console.log('DOM loaded, looking for LaoID button...');

	const laoidBtn = document.getElementById('laoid-signin');
	if (laoidBtn) {
		console.log('✅ LaoID button found!');

		// Add hover effects
		laoidBtn.addEventListener('mouseover', function() {
			this.style.backgroundColor = '#e55a00';
		});

		laoidBtn.addEventListener('mouseout', function() {
			this.style.backgroundColor = '#ff6600';
		});

		// Add click handler for debugging
		laoidBtn.addEventListener('click', function(e) {
			console.log('🔥 LaoID button clicked!');

			// Check if SDK is available
			if (typeof window.LaoIdSSO !== 'undefined') {
				console.log('✅ LaoID SDK is available');

				// Debug: Check current meta tag values
				const clientIdMeta = document.querySelector('meta[name="laoid-signin-client-id"]');
				const redirectUriMeta = document.querySelector('meta[name="laoid-signin-redirect-uri"]');
				const useCallbackMeta = document.querySelector('meta[name="laoid-signin-use-callback-uri"]');

				const currentClientId = clientIdMeta ? clientIdMeta.getAttribute('content') : 'NOT FOUND';
				const currentRedirectUri = redirectUriMeta ? redirectUriMeta.getAttribute('content') : 'NOT FOUND';
				const currentUseCallback = useCallbackMeta ? useCallbackMeta.getAttribute('content') : 'NOT FOUND';

				console.log('🔍 Current Meta Values:');
				console.log('  Client ID:', currentClientId);
				console.log('  Redirect URI:', currentRedirectUri);
				console.log('  Use Callback:', currentUseCallback);

				// Check if client ID is still placeholder
				if (currentClientId === 'YOUR_CLIENT_ID_HERE') {
					console.warn('⚠️ Client ID is still placeholder! Need to update with real ClientID from LaoCRM');
					alert('⚠️ Cần cập nhật ClientID từ LaoCRM!\n\nClient ID hiện tại: ' + currentClientId);
					return;
				}

				// Try manual redirect since SDK auto-handling might not work
				console.log('🔄 Trying manual redirect...');
				const loginUrl = `https://demo-sso.tinasoft.io/login?client_id=${encodeURIComponent(currentClientId)}&redirect_uri=${encodeURIComponent(currentRedirectUri)}&use_callback_uri=${currentUseCallback}`;
				console.log('🔗 Opening:', loginUrl);
				window.location.href = loginUrl;

			} else {
				console.warn('❌ LaoID SDK not loaded, will try manual redirect');
				// Fallback: manual redirect
				const clientId = '660dfa27-5a95-4c88-8a55-abe1310bf579';
				const redirectUri = 'http://localhost';
				const loginUrl = `https://demo-sso.tinasoft.io/login?client_id=${clientId}&redirect_uri=${encodeURIComponent(redirectUri)}&use_callback_uri=true`;
				window.location.href = loginUrl;
			}
		});

		console.log('✅ Event listeners added to LaoID button');
	} else {
		console.error('❌ LaoID button not found!');
	}

	// Test button for comparison
	const testBtn = document.getElementById('test-btn');
	if (testBtn) {
		testBtn.addEventListener('click', function() {
			alert('Test button works!');
			console.log('✅ Test button clicked successfully');
		});
		console.log('✅ Test button event listener added');
	}



	// Check if SDK is loaded and debug config
	setTimeout(function() {
		if (typeof window.LaoIdSSO !== 'undefined') {
			console.log('✅ LaoID Demo SDK loaded successfully');
			console.log('🔍 SDK Object:', window.LaoIdSSO);

			// Debug: Check what config SDK is reading
			const clientIdMeta = document.querySelector('meta[name="laoid-signin-client-id"]');
			const redirectUriMeta = document.querySelector('meta[name="laoid-signin-redirect-uri"]');
			const useCallbackMeta = document.querySelector('meta[name="laoid-signin-use-callback-uri"]');

			console.log('🔍 Meta Tags Config:');
			console.log('  Client ID:', clientIdMeta ? clientIdMeta.getAttribute('content') : 'NOT FOUND');
			console.log('  Redirect URI:', redirectUriMeta ? redirectUriMeta.getAttribute('content') : 'NOT FOUND');
			console.log('  Use Callback:', useCallbackMeta ? useCallbackMeta.getAttribute('content') : 'NOT FOUND');

			console.log('🔍 SDK Config:');
			console.log('  SDK Client ID:', window.LaoIdSSO.clientId);
			console.log('  SDK Redirect URI:', window.LaoIdSSO.redirectUri);
			console.log('  SDK Use Callback:', window.LaoIdSSO.useCallbackUri);
			console.log('  SDK API Endpoint:', window.LaoIdSSO.apiEndpoint);
		} else {
			console.warn('⚠️ LaoID SDK not loaded after 2 seconds');
		}
	}, 2000);

	// Listen for LaoID callback messages
	window.addEventListener('message', function(event) {
		if (event.origin !== 'https://demo-sso.tinasoft.io') {
			return; // Only accept messages from LaoID Demo
		}

		if (event.data && event.data.type === 'LAOID_AUTH_SUCCESS') {
			console.log('✅ LaoID authentication successful!');
			console.log('Authorization code:', event.data.authCode);

			// TODO: Send auth code to server for token exchange
			alert('LaoID login successful! Auth code: ' + event.data.authCode.substring(0, 20) + '...');

			// For now, redirect to main page
			// window.location.href = '/';
		}
	});
});

// Test function for different callback URIs
function testCallback(callbackUri) {
	console.log('🧪 Testing callback URI:', callbackUri);

	// Update meta tag
	const metaRedirectUri = document.querySelector('meta[name="laoid-signin-redirect-uri"]');
	if (metaRedirectUri) {
		metaRedirectUri.setAttribute('content', callbackUri);
		console.log('✅ Updated meta tag to:', callbackUri);
	}

	// Manual redirect for testing
	const clientId = '660dfa27-5a95-4c88-8a55-abe1310bf579';
	const loginUrl = `https://sso.laoid.net/login?client_id=${clientId}&redirect_uri=${encodeURIComponent(callbackUri)}&use_callback_uri=true`;

	console.log('🔗 Opening:', loginUrl);
	window.open(loginUrl, '_blank');
}

// Test function for different client IDs
function testClientId(clientId) {
	console.log('🔑 Testing client ID:', clientId);

	// Update meta tag
	const metaClientId = document.querySelector('meta[name="laoid-signin-client-id"]');
	if (metaClientId) {
		metaClientId.setAttribute('content', clientId);
		console.log('✅ Updated client ID meta tag to:', clientId);
	}

	// Manual redirect for testing
	const redirectUri = 'https://localhost';
	const loginUrl = `https://sso.laoid.net/login?client_id=${clientId}&redirect_uri=${encodeURIComponent(redirectUri)}&use_callback_uri=true`;

	console.log('🔗 Opening:', loginUrl);
	window.open(loginUrl, '_blank');
}

// Test with real ClientID
function testWithRealClientID() {
	console.log('🧪 Testing with real ClientID...');

	const clientId = '660dfa27-5a95-4c88-8a55-abe1310bf579';
	const redirectUri = 'http://localhost:8888';
	const loginUrl = `https://sso.laoid.net/login?client_id=${clientId}&redirect_uri=${encodeURIComponent(redirectUri)}&use_callback_uri=true`;

	console.log('🔗 Opening with real ClientID:', loginUrl);
	window.location.href = loginUrl;
}


</script>
