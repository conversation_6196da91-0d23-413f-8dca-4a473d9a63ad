<?php

/**
 * SPDX-FileCopyrightText: 2016-2024 Nextcloud GmbH and Nextcloud contributors
 * SPDX-FileCopyrightText: 2011-2016 ownCloud, Inc.
 * SPDX-License-Identifier: AGPL-3.0-only
 *
 * @var \OCP\IL10N $l
 */
// script('core', 'login');
?>

	<div id="login"></div>

	<!-- LaoId SSO Login Button -->
	<div class="laoid-container">
		<button id="laoid-signin">
			Login by LaoId
		</button>
	</div>


<script nonce="<?php p($_['cspNonce']); ?>">
document.addEventListener('DOMContentLoaded', function() {
	const laoidBtn = document.getElementById('laoid-signin');
	if (laoidBtn) {

		laoidBtn.addEventListener('click', function(e) {
			const clientId = '660dfa27-5a95-4c88-8a55-abe1310bf579';
			const redirectUri = 'http://localhost';
			const loginUrl = `https://demo-sso.tinasoft.io/login?client_id=${clientId}&redirect_uri=${encodeURIComponent(redirectUri)}&use_callback_uri=true`;
			window.open(loginUrl, '_blank');
		});
	}



	// Function to handle authorization code and get user info
	async function handleAuthorizationCode(authorizationCode) {
		try {
			// Step 1: Exchange authorization code for access token
			const tokenResponse = await fetch('https://demo-sso.tinasoft.io/api/v1/third-party/verify', {
				method: 'POST',
				headers: {
					'Content-Type': 'application/json',
					'Accept': 'application/json',
					'X-Accept-Language': 'vi'
				},
				body: JSON.stringify({
					code: authorizationCode,
					clientId: '660dfa27-5a95-4c88-8a55-abe1310bf579',
					clientSecret: 'df1699140bcb456eaa6d85d54c5fbd79'
				})
			});

			const tokenData = await tokenResponse.json();

			if (tokenData.success && tokenData.data && tokenData.data.accessToken) {
				// Step 3: Get user information using access token
				const userResponse = await fetch('https://demo-sso.tinasoft.io/api/v1/third-party/me', {
					method: 'GET',
					headers: {
						'Content-Type': 'application/json',
						'Accept': 'application/json',
						'X-Accept-Language': 'vi',
						'Authorization': 'Bearer ' + tokenData.data.accessToken,
						'x-api-key': '660dfa27-5a95-4c88-8a55-abe1310bf579'
					}
				});

				const userData = await userResponse.json();

				if (userData.success && userData.data) {
					// Display the complete API response including success, message, data, statusCode
					displayUserInfo(userData);
				} else {
					alert('Failed to get user information: ' + (userData.message || 'Unknown error'));
				}
			} else {
				alert('Failed to get access token: ' + (tokenData.message || 'Unknown error'));
			}
		} catch (error) {
			alert('Error: ' + error.message);
		}
	}

	// Function to display user information
	function displayUserInfo(userInfo) {
		// Create a display div for full JSON response
		const userDisplay = document.createElement('div');
		userDisplay.style.cssText = `
			position: fixed;
			top: 20px;
			right: 20px;
			background: #fff;
			border: 2px solid #ff6600;
			border-radius: 10px;
			padding: 20px;
			box-shadow: 0 4px 12px rgba(0,0,0,0.15);
			z-index: 10000;
			max-width: 600px;
			max-height: 80vh;
			overflow-y: auto;
			font-family: 'Courier New', monospace;
			font-size: 12px;
		`;

		// Display full JSON response
		userDisplay.innerHTML = `
			<div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 15px;">
				<h3 style="margin: 0; color: #ff6600; font-size: 18px; font-family: Arial, sans-serif;">
					🎉 LaoID API Response
				</h3>
				<button onclick="this.parentElement.parentElement.remove()" style="
					background: #ff6600;
					color: white;
					border: none;
					padding: 5px 10px;
					border-radius: 3px;
					cursor: pointer;
					font-size: 12px;
				">✕</button>
			</div>
			<div style="
				background: #f5f5f5;
				border: 1px solid #ddd;
				border-radius: 5px;
				padding: 15px;
				white-space: pre-wrap;
				word-wrap: break-word;
				line-height: 1.4;
			">${JSON.stringify(userInfo, null, 2)}</div>
			<div style="margin-top: 15px; text-align: center;">
				<button onclick="navigator.clipboard.writeText('${JSON.stringify(userInfo, null, 2).replace(/'/g, "\\'")}'); alert('JSON copied to clipboard!')" style="
					background: #28a745;
					color: white;
					border: none;
					padding: 8px 16px;
					border-radius: 5px;
					cursor: pointer;
					font-size: 12px;
					margin-right: 10px;
					font-family: Arial, sans-serif;
				">📋 Copy JSON</button>
				<button onclick="this.parentElement.parentElement.remove()" style="
					background: #dc3545;
					color: white;
					border: none;
					padding: 8px 16px;
					border-radius: 5px;
					cursor: pointer;
					font-size: 12px;
					font-family: Arial, sans-serif;
				">🗑️ Close</button>
			</div>
		`;

		document.body.appendChild(userDisplay);

		// Auto remove after 120 seconds (longer time for JSON viewing)
		setTimeout(() => {
			if (userDisplay.parentElement) {
				userDisplay.remove();
			}
		}, 120000);
	}

	// Check for authorization code in URL
	
	function checkUrlForAuthCode() {
		const urlParams = new URLSearchParams(window.location.search);
		const authCode = urlParams.get('authorization_code');
		if (authCode) {
			handleAuthorizationCode(authCode);

			const cleanUrl = window.location.pathname;
			window.history.replaceState({}, document.title, cleanUrl);
		}
	}

	// Check URL on page load
	checkUrlForAuthCode();
});
</script>
